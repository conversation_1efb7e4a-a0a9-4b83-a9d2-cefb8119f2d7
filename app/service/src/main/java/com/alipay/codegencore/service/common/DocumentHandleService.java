/**
 * 文档处理服务接口
 */
package com.alipay.codegencore.service.common;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.domain.DocumentDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.model.SegmentInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 文档处理服务
 */
public interface DocumentHandleService {

    /**
     * 上传会话文件
     *
     * @param sessionUid 会话UID
     * @param fileList   文件列表
     */
    void uploadSessionDocument(String sessionUid, List<MultipartFile> fileList, UserAuthDO userAuthDO, String segmentationStrategy);

    /**
     * 上传场景文件
     *
     * @param sceneId  场景ID
     * @param fileList 文件列表
     */
    void uploadSceneDocument(Long sceneId, List<MultipartFile> fileList, UserAuthDO userAuthDO, String segmentationStrategy);


    /**
     * 助手绑定语雀知识库的文档
     *
     * @param sceneId              场景ID
     * @param userAuthDO           用户权限信息
     * @param segmentationStrategy 分段策略
     * @param yuQueToken           语雀token
     * @param bookId               语雀书库ID
     * @param docIdList            文档ID列表
     */
    void uploadSceneYuQueBook(Long sceneId, UserAuthDO userAuthDO, String segmentationStrategy, String yuQueToken, Long bookId, List<Long> docIdList, List<String> docSlugList);

    /**
     * 摘要文件
     *
     * @param documentUid 文件UID
     * @return 摘要结果
     */
    String summeryDocument(String documentUid);

    /**
     * 检索段落(向量召回)
     *
     * @param sessionUid 会话UID
     * @param query      查询关键字
     * @return 段落信息列表
     */
    List<SegmentInfo> recallSegment(String sessionUid, String query);

    /**
     * 检索段落(向量召回)
     *
     * @param sceneId    agentId
     * @param query      查询关键字
     * @param topNSimilarity 返回数量限制
     * @param minSimilarity 最小相似度阈值，如果为null则使用配置中的默认值
     * @return 段落信息列表
     */
    List<SegmentInfo> recallSegmentBySceneId(Long sceneId, String query, Integer topNSimilarity, Double minSimilarity);

    /**
     * 总结文本
     * @param content
     * @param empId
     * @param documentChatConfig
     * @return
     */
    String summeryContent(String content, String empId, JSONObject documentChatConfig);

    /**
     * 解析文档
     * @param documentUid
     * @param documentChatConfig
     * @param empId
     */
    void parseFile(String documentUid, JSONObject documentChatConfig, String empId);
    /**
     * 删除文档或知识库
     * <AUTHOR>
     * @since 2024.01.18
     * @param documentUid documentUid
     */
    void deleteSceneDocument(String documentUid, Long sceneId);
    /**
     * 获取document的docIdList
     * <AUTHOR>
     * @since 2024.01.18
     * @param documentId documentId
     * @return java.util.List<java.lang.Long>
     */
    List<Long> getDocIdsFromDocument(Long documentId);
    /**
     * 获取document的docSlugList
     * <AUTHOR>
     * @since 2024.01.18
     * @param documentId documentId
     * @return java.util.List<java.lang.Long>
     */
    List<String> getDocSlugListFromDocument(Long documentId);
    /**
     * 检查document是否能解绑
     * <AUTHOR>
     * @since 2024.01.22
     * @param documentUid documentUid
     * @return java.lang.Boolean
     */
    Boolean checkDeleteDocument(String documentUid);
    /**
     * 通过documentUid获取documentDO
     * <AUTHOR>
     * @since 2024.01.23
     * @param documentUid documentUid
     * @return com.alipay.codegencore.model.domain.DocumentDO
     */
    DocumentDO getDocumentByUid(String documentUid);

    /**
     * 通过documentUid获取查询多个documentDO
     * @param documentUid
     * @return
     */
    List<DocumentDO> getDocumentByUidList(List<String> documentUid);
    /**
     * 获取一个助手的document列表
     * <AUTHOR>
     * @since 2024.01.24
     * @param sceneId sceneId
     * @return java.util.List<com.alipay.codegencore.model.domain.DocumentDO>
     */
    List<DocumentDO> getDocumentBySceneId(Long sceneId);
    /**
     * 校验知识库是否已存在
     * <AUTHOR>
     * @since 2024.01.24
     * @param sceneId sceneId
     * @param bookId bookId
     * @return java.lang.Boolean
     */
    Boolean checkYuQueBookExist(Long sceneId, Long bookId);

    /**
     * 定时任务更新语雀文档
     *
     * @param documentDO
     * @throws IOException
     */
    void updateYuQueDocument(DocumentDO documentDO,JSONObject documentChatConfig) throws IOException;
}